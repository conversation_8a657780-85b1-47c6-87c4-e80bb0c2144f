import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withDelay,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Eye, 
  Heart, 
  Zap, 
  MessageCircle, 
  Filter, 
  TrendingUp, 
  Shield, 
  Star,
  Sparkles,
  Crown
} from 'lucide-react-native';
import { PremiumFeature } from '@/types/premium';
import { theme } from '@/constants/theme';

interface PremiumFeatureShowcaseProps {
  features: PremiumFeature[];
  isPremium?: boolean;
}

const iconMap = {
  'eye': Eye,
  'heart': Heart,
  'zap': Zap,
  'message-circle': MessageCircle,
  'filter': Filter,
  'trending-up': TrendingUp,
  'shield': Shield,
  'star': Star,
};

export default function PremiumFeatureShowcase({ 
  features, 
  isPremium = false 
}: PremiumFeatureShowcaseProps) {
  const containerOpacity = useSharedValue(0);
  const containerTranslateY = useSharedValue(30);

  useEffect(() => {
    containerOpacity.value = withTiming(1, { duration: 600 });
    containerTranslateY.value = withSpring(0, {
      damping: 15,
      stiffness: 150,
    });
  }, []);

  const containerStyle = useAnimatedStyle(() => {
    return {
      opacity: containerOpacity.value,
      transform: [{ translateY: containerTranslateY.value }],
    };
  });

  const FeatureCard = ({ 
    feature, 
    index 
  }: { 
    feature: PremiumFeature; 
    index: number 
  }) => {
    const scale = useSharedValue(0);
    const opacity = useSharedValue(0);
    const highlightScale = useSharedValue(feature.highlight ? 1 : 0);
    const sparkleRotation = useSharedValue(0);

    useEffect(() => {
      const delay = index * 100;
      
      setTimeout(() => {
        scale.value = withSpring(1, {
          damping: 15,
          stiffness: 200,
        });
        opacity.value = withTiming(1, { duration: 400 });
      }, delay);

      if (feature.highlight) {
        setTimeout(() => {
          highlightScale.value = withSpring(1, {
            damping: 12,
            stiffness: 300,
          });
        }, delay + 200);
      }

      // Continuous sparkle rotation for highlighted features
      if (feature.highlight) {
        sparkleRotation.value = withDelay(
          delay + 400,
          withTiming(360, { duration: 3000 }, () => {
            sparkleRotation.value = 0;
            sparkleRotation.value = withTiming(360, { duration: 3000 });
          })
        );
      }
    }, []);

    const cardStyle = useAnimatedStyle(() => {
      return {
        transform: [{ scale: scale.value }],
        opacity: opacity.value,
      };
    });

    const highlightStyle = useAnimatedStyle(() => {
      return {
        transform: [{ scale: highlightScale.value }],
        opacity: highlightScale.value,
      };
    });

    const sparkleStyle = useAnimatedStyle(() => {
      return {
        transform: [{ rotate: `${sparkleRotation.value}deg` }],
      };
    });

    const IconComponent = iconMap[feature.icon as keyof typeof iconMap] || Star;

    return (
      <Animated.View
        style={[
          styles.featureCard,
          feature.highlight && styles.highlightFeature,
          cardStyle,
        ]}
      >
        {/* Highlight Background */}
        {feature.highlight && (
          <Animated.View style={[styles.highlightBackground, highlightStyle]}>
            <LinearGradient
              colors={['rgba(139, 92, 246, 0.1)', 'rgba(236, 72, 153, 0.1)']}
              style={styles.highlightGradient}
            />
          </Animated.View>
        )}

        {/* Feature Icon */}
        <View style={[
          styles.featureIcon,
          feature.highlight && styles.highlightIcon,
          isPremium && styles.premiumIcon,
        ]}>
          <IconComponent 
            size={24} 
            color={
              feature.highlight 
                ? '#FFD700' 
                : isPremium 
                  ? theme.colors.primary 
                  : theme.colors.gray[600]
            } 
          />
        </View>

        {/* Feature Content */}
        <View style={styles.featureContent}>
          <View style={styles.featureTitleRow}>
            <Text style={[
              styles.featureTitle,
              feature.highlight && styles.highlightTitle,
              isPremium && styles.premiumTitle,
            ]}>
              {feature.name}
            </Text>
            
            {/* New Badge */}
            {feature.isNew && (
              <View style={styles.newBadge}>
                <Text style={styles.newBadgeText}>NEW</Text>
              </View>
            )}
            
            {/* Coming Soon Badge */}
            {feature.comingSoon && (
              <View style={styles.comingSoonBadge}>
                <Text style={styles.comingSoonBadgeText}>SOON</Text>
              </View>
            )}
          </View>
          
          <Text style={[
            styles.featureDescription,
            isPremium && styles.premiumDescription,
          ]}>
            {feature.description}
          </Text>
        </View>

        {/* Highlight Badge */}
        {feature.highlight && (
          <Animated.View style={[styles.highlightBadge, highlightStyle]}>
            <Animated.View style={sparkleStyle}>
              <Sparkles size={16} color="#FFD700" />
            </Animated.View>
          </Animated.View>
        )}

        {/* Premium Badge */}
        {isPremium && (
          <View style={styles.premiumBadge}>
            <Crown size={14} color={theme.colors.primary} />
          </View>
        )}

        {/* Category Indicator */}
        <View style={[
          styles.categoryIndicator,
          { backgroundColor: getCategoryColor(feature.category) }
        ]} />
      </Animated.View>
    );
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'discovery':
        return theme.colors.primary;
      case 'messaging':
        return theme.colors.success;
      case 'privacy':
        return theme.colors.warning;
      case 'boost':
        return theme.colors.error;
      case 'support':
        return theme.colors.info;
      default:
        return theme.colors.gray400;
    }
  };

  return (
    <Animated.View style={[styles.container, containerStyle]}>
      <View style={styles.header}>
        <Text style={styles.title}>Premium Features</Text>
        {isPremium && (
          <View style={styles.premiumIndicator}>
            <Crown size={16} color={theme.colors.primary} />
            <Text style={styles.premiumText}>Active</Text>
          </View>
        )}
      </View>
      
      <View style={styles.featuresGrid}>
        {features.map((feature, index) => (
          <FeatureCard 
            key={feature.id} 
            feature={feature} 
            index={index} 
          />
        ))}
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.xl,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
  },
  premiumIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
    backgroundColor: theme.colors.primary + '20',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
  },
  premiumText: {
    fontSize: theme.fontSize.xs,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.primary,
  },
  featuresGrid: {
    gap: theme.spacing.md,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  highlightFeature: {
    borderColor: theme.colors.primary + '40',
    borderWidth: 2,
  },
  highlightBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: theme.borderRadius.lg,
  },
  highlightGradient: {
    flex: 1,
    borderRadius: theme.borderRadius.lg,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.md,
  },
  highlightIcon: {
    backgroundColor: theme.colors.primary + '20',
  },
  premiumIcon: {
    backgroundColor: theme.colors.primary + '10',
  },
  featureContent: {
    flex: 1,
  },
  featureTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
    marginBottom: 2,
  },
  featureTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.text,
  },
  highlightTitle: {
    color: theme.colors.primary,
  },
  premiumTitle: {
    color: theme.colors.primary,
  },
  featureDescription: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray[600],
    lineHeight: 18,
  },
  premiumDescription: {
    color: theme.colors.gray[700],
  },
  newBadge: {
    backgroundColor: theme.colors.success,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  newBadgeText: {
    fontSize: 10,
    fontWeight: theme.fontWeight.bold,
    color: 'white',
  },
  comingSoonBadge: {
    backgroundColor: theme.colors.warning,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  comingSoonBadgeText: {
    fontSize: 10,
    fontWeight: theme.fontWeight.bold,
    color: 'white',
  },
  highlightBadge: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
  },
  premiumBadge: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
  },
  categoryIndicator: {
    position: 'absolute',
    left: 0,
    top: '50%',
    width: 3,
    height: 20,
    borderTopRightRadius: 2,
    borderBottomRightRadius: 2,
    transform: [{ translateY: -10 }],
  },
});

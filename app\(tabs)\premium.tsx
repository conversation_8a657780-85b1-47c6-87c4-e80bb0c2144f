import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Crown, Sparkles } from 'lucide-react-native';
import { usePremiumStore } from '@/stores/premiumStore';
import { PREMIUM_FEATURES } from '@/types/premium';
import PremiumSkeleton from '@/components/premium/PremiumSkeleton';
import AnimatedSubscriptionCard from '@/components/premium/AnimatedSubscriptionCard';
import PremiumFeatureShowcase from '@/components/premium/PremiumFeatureShowcase';
import PaymentProcessingModal from '@/components/premium/PaymentProcessingModal';
import PremiumButton from '@/components/premium/PremiumButton';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';

export default function PremiumTab() {
  const {
    availablePlans,
    selectedPlan,
    isPremium,
    isLoading,
    isLoadingPlans,
    isProcessingPayment,
    error,
    loadPlans,
    loadSubscription,
    selectPlan,
    subscribe,
    restorePurchases,
    clearError,
  } = usePremiumStore();

  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentError, setPaymentError] = useState(false);

  useEffect(() => {
    loadPlans();
    loadSubscription();
  }, []); // Empty dependency array is safe for Zustand store actions

  const handlePlanSelect = (planId: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    selectPlan(planId);
  };

  const handleSubscribe = async () => {
    if (!selectedPlan) return;

    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    setShowPaymentModal(true);
    setPaymentSuccess(false);
    setPaymentError(false);

    try {
      const success = await subscribe(selectedPlan);
      if (success) {
        setPaymentSuccess(true);
        if (Platform.OS !== 'web') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }
      } else {
        setPaymentError(true);
        if (Platform.OS !== 'web') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        }
      }
    } catch (error) {
      setPaymentError(true);
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
    }
  };

  const handleRestorePurchases = async () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    const success = await restorePurchases();
    if (success) {
      Alert.alert('Success', 'Purchases restored successfully!');
    } else {
      Alert.alert('No Purchases Found', 'No previous purchases were found to restore.');
    }
  };

  const closePaymentModal = () => {
    setShowPaymentModal(false);
    setPaymentSuccess(false);
    setPaymentError(false);
  };

  // Show loading skeleton while data is loading
  if (isLoading || isLoadingPlans) {
    return (
      <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <PremiumSkeleton variant="full" />
        </SafeAreaView>
      </LinearGradient>
    );
  }

  const selectedPlanData = availablePlans.find(p => p.id === selectedPlan);

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.crownContainer}>
              <Crown size={48} color="#FFD700" />
              <View style={styles.sparklesContainer}>
                <Sparkles size={20} color="#FFD700" style={styles.sparkle1} />
                <Sparkles size={16} color="#FFD700" style={styles.sparkle2} />
                <Sparkles size={14} color="#FFD700" style={styles.sparkle3} />
              </View>
            </View>
            <Text style={styles.title}>
              {isPremium ? 'Premium Active' : 'SoulSync Premium'}
            </Text>
            <Text style={styles.subtitle}>
              {isPremium
                ? 'Enjoy all premium features and find your perfect match'
                : 'Unlock premium features and find your perfect match faster'
              }
            </Text>
            {isPremium && (
              <View style={styles.premiumBadge}>
                <Crown size={16} color="#FFD700" />
                <Text style={styles.premiumBadgeText}>Premium Member</Text>
              </View>
            )}
          </View>

          {/* Premium Features */}
          <View style={styles.featuresSection}>
            <PremiumFeatureShowcase
              features={PREMIUM_FEATURES}
              isPremium={isPremium}
            />
          </View>

          {/* Subscription Plans */}
          {!isPremium && (
            <View style={styles.plansSection}>
              <Text style={styles.sectionTitle}>Choose Your Plan</Text>
              <View style={styles.plansContainer}>
                {availablePlans.map((plan, index) => (
                  <AnimatedSubscriptionCard
                    key={plan.id}
                    plan={plan}
                    isSelected={selectedPlan === plan.id}
                    onSelect={handlePlanSelect}
                    index={index}
                  />
                ))}
              </View>
            </View>
          )}

          {/* Free vs Premium Comparison */}
          {!isPremium && (
            <View style={styles.comparisonSection}>
              <Text style={styles.sectionTitle}>Free vs Premium</Text>
              <View style={styles.comparisonTable}>
                <View style={styles.comparisonHeader}>
                  <Text style={styles.comparisonHeaderText}>Feature</Text>
                  <Text style={styles.comparisonHeaderText}>Free</Text>
                  <Text style={styles.comparisonHeaderText}>Premium</Text>
                </View>

                <View style={styles.comparisonRow}>
                  <Text style={styles.comparisonFeature}>Daily Likes</Text>
                  <Text style={styles.freeValue}>10</Text>
                  <Text style={styles.premiumValue}>Unlimited</Text>
                </View>

                <View style={styles.comparisonRow}>
                  <Text style={styles.comparisonFeature}>Super Likes</Text>
                  <Text style={styles.freeValue}>1/day</Text>
                  <Text style={styles.premiumValue}>5/day</Text>
                </View>

                <View style={styles.comparisonRow}>
                  <Text style={styles.comparisonFeature}>See Who Likes You</Text>
                  <Text style={styles.freeValue}>✗</Text>
                  <Text style={styles.premiumValue}>✓</Text>
                </View>

                <View style={styles.comparisonRow}>
                  <Text style={styles.comparisonFeature}>Profile Boost</Text>
                  <Text style={styles.freeValue}>✗</Text>
                  <Text style={styles.premiumValue}>✓</Text>
                </View>

                <View style={styles.comparisonRow}>
                  <Text style={styles.comparisonFeature}>Advanced Filters</Text>
                  <Text style={styles.freeValue}>✗</Text>
                  <Text style={styles.premiumValue}>✓</Text>
                </View>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          {!isPremium ? (
            <TouchableOpacity
              style={[styles.subscribeButton, isProcessingPayment && styles.disabledButton]}
              onPress={handleSubscribe}
              disabled={isProcessingPayment}
            >
              <LinearGradient
                colors={['#8B5CF6', '#EC4899']}
                style={styles.subscribeGradient}
              >
                <Crown size={20} color="white" />
                <Text style={styles.subscribeButtonText}>
                  {isProcessingPayment
                    ? 'Processing...'
                    : `Start Premium - ${selectedPlanData?.pricePerMonth || '$19.99/month'}`
                  }
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          ) : (
            <View style={styles.premiumActiveFooter}>
              <Crown size={24} color="#FFD700" />
              <Text style={styles.premiumActiveText}>Premium Active</Text>
              <Text style={styles.premiumActiveSubtext}>Enjoy all premium features!</Text>
            </View>
          )}

          <View style={styles.footerLinks}>
            <TouchableOpacity>
              <Text style={styles.footerLink}>Terms of Service</Text>
            </TouchableOpacity>
            <Text style={styles.footerSeparator}>•</Text>
            <TouchableOpacity>
              <Text style={styles.footerLink}>Privacy Policy</Text>
            </TouchableOpacity>
            <Text style={styles.footerSeparator}>•</Text>
            <TouchableOpacity onPress={handleRestorePurchases}>
              <Text style={styles.footerLink}>Restore Purchase</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Payment Processing Modal */}
        <PaymentProcessingModal
          visible={showPaymentModal}
          plan={selectedPlanData || null}
          isProcessing={isProcessingPayment}
          isSuccess={paymentSuccess}
          isError={paymentError}
          errorMessage={error || undefined}
          onClose={closePaymentModal}
        />
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
    paddingTop: theme.spacing.xxl,
    paddingBottom: theme.spacing.xl,
    position: 'relative',
    minHeight: 200,
  },
  crownContainer: {
    marginBottom: theme.spacing.xl,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 215, 0, 0.15)',
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  sparklesContainer: {
    position: 'absolute',
    top: -20,
    left: -20,
    right: -20,
    bottom: -20,
  },
  sparkle1: {
    position: 'absolute',
    top: 5,
    right: 10,
  },
  sparkle2: {
    position: 'absolute',
    bottom: 15,
    left: 5,
  },
  sparkle3: {
    position: 'absolute',
    top: '40%',
    right: -10,
  },
  title: {
    fontSize: theme.fontSize.xxxxl,
    fontWeight: theme.fontWeight.extrabold,
    color: 'white',
    marginBottom: theme.spacing.md,
    textAlign: 'center',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: theme.fontSize.lg,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 26,
    marginBottom: theme.spacing.lg,
    fontWeight: theme.fontWeight.medium,
    maxWidth: 280,
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
    backgroundColor: 'rgba(255, 215, 0, 0.25)',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.full,
    borderWidth: 2,
    borderColor: '#FFD700',
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 8,
  },
  premiumBadgeText: {
    color: '#FFD700',
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.bold,
  },
  featuresSection: {
    paddingHorizontal: theme.spacing.xl,
    marginBottom: theme.spacing.xxl,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    marginHorizontal: theme.spacing.lg,
    borderRadius: theme.borderRadius.xl,
    paddingVertical: theme.spacing.xl,
  },
  sectionTitle: {
    fontSize: theme.fontSize.xxl,
    fontWeight: theme.fontWeight.extrabold,
    color: 'white',
    marginBottom: theme.spacing.xl,
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  plansSection: {
    paddingHorizontal: theme.spacing.xl,
    marginBottom: theme.spacing.xxl,
  },
  plansContainer: {
    gap: theme.spacing.lg,
  },
  comparisonSection: {
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.xl,
  },
  comparisonTable: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
  },
  comparisonHeader: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
  },
  comparisonHeaderText: {
    flex: 1,
    fontSize: theme.fontSize.sm,
    fontWeight: theme.fontWeight.bold,
    color: 'white',
    textAlign: 'center',
  },
  comparisonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  comparisonFeature: {
    flex: 1,
    fontSize: theme.fontSize.sm,
    color: 'white',
  },
  freeValue: {
    flex: 1,
    fontSize: theme.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  premiumValue: {
    flex: 1,
    fontSize: theme.fontSize.sm,
    color: '#FFD700',
    fontWeight: theme.fontWeight.medium,
    textAlign: 'center',
  },
  footer: {
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.xl,
    paddingTop: theme.spacing.md,
  },
  subscribeButton: {
    borderRadius: theme.borderRadius.xl,
    marginBottom: theme.spacing.lg,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  disabledButton: {
    opacity: 0.7,
  },
  subscribeGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.xl,
    gap: theme.spacing.sm,
  },
  subscribeButtonText: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.bold,
    color: 'white',
  },
  premiumActiveFooter: {
    alignItems: 'center',
    paddingVertical: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  premiumActiveText: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: '#FFD700',
    marginTop: theme.spacing.sm,
  },
  premiumActiveSubtext: {
    fontSize: theme.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: theme.spacing.xs,
  },
  footerLinks: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  footerLink: {
    fontSize: theme.fontSize.xs,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  footerSeparator: {
    fontSize: theme.fontSize.xs,
    color: 'rgba(255, 255, 255, 0.5)',
  },
});
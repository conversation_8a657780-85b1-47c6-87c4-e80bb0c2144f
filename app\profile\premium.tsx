import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Platform,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import { ArrowLeft, Crown, Sparkles } from 'lucide-react-native';
import { usePremiumStore } from '@/stores/premiumStore';
import { PREMIUM_FEATURES } from '@/types/premium';
import PremiumSkeleton from '@/components/premium/PremiumSkeleton';
import AnimatedSubscriptionCard from '@/components/premium/AnimatedSubscriptionCard';
import PremiumFeatureShowcase from '@/components/premium/PremiumFeatureShowcase';
import PaymentProcessingModal from '@/components/premium/PaymentProcessingModal';

const { width } = Dimensions.get('window');

export default function PremiumScreen() {
  const router = useRouter();
  const {
    availablePlans,
    selectedPlan,
    isPremium,
    subscription,
    isLoading,
    isLoadingPlans,
    isProcessingPayment,
    error,
    loadPlans,
    loadSubscription,
    selectPlan,
    subscribe,
    restorePurchases,
    clearError,
  } = usePremiumStore();

  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentError, setPaymentError] = useState(false);

  useEffect(() => {
    loadPlans();
    loadSubscription();
  }, []);

  const handlePlanSelect = (planId: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    selectPlan(planId);
  };

  const handleSubscribe = async () => {
    if (!selectedPlan) return;

    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    setShowPaymentModal(true);
    setPaymentSuccess(false);
    setPaymentError(false);

    try {
      const success = await subscribe(selectedPlan);
      if (success) {
        setPaymentSuccess(true);
        if (Platform.OS !== 'web') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }
      } else {
        setPaymentError(true);
        if (Platform.OS !== 'web') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        }
      }
    } catch (error) {
      setPaymentError(true);
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
    }
  };

  const handleRestorePurchases = async () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    const success = await restorePurchases();
    if (success) {
      Alert.alert('Success', 'Purchases restored successfully!');
    } else {
      Alert.alert('No Purchases Found', 'No previous purchases were found to restore.');
    }
  };

  const closePaymentModal = () => {
    setShowPaymentModal(false);
    setPaymentSuccess(false);
    setPaymentError(false);
  };

  const handleGoBack = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  // Show loading skeleton while data is loading
  if (isLoading || isLoadingPlans) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Premium Features</Text>
          <View style={styles.placeholder} />
        </View>
        <PremiumSkeleton variant="full" />
      </SafeAreaView>
    );
  }

  const selectedPlanData = availablePlans.find(p => p.id === selectedPlan);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <ArrowLeft size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isPremium ? 'Premium Active' : 'Premium Features'}
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <LinearGradient
          colors={['#8B5CF6', '#EC4899']}
          style={styles.heroSection}
        >
          <View style={styles.crownContainer}>
            <Crown size={48} color="#FFD700" />
            <View style={styles.sparklesContainer}>
              <Sparkles size={20} color="#FFD700" style={styles.sparkle1} />
              <Sparkles size={16} color="#FFD700" style={styles.sparkle2} />
              <Sparkles size={14} color="#FFD700" style={styles.sparkle3} />
            </View>
          </View>
          <Text style={styles.heroTitle}>
            {isPremium ? 'Premium Active' : 'Unlock Premium'}
          </Text>
          <Text style={styles.heroSubtitle}>
            {isPremium
              ? 'Enjoy all premium features and find your perfect match'
              : 'Get unlimited access to all premium features and find your perfect match faster'
            }
          </Text>
          {isPremium && (
            <View style={styles.premiumBadge}>
              <Crown size={16} color="#FFD700" />
              <Text style={styles.premiumBadgeText}>Premium Member</Text>
            </View>
          )}
        </LinearGradient>

        {/* Premium Features */}
        <View style={styles.featuresSection}>
          <PremiumFeatureShowcase
            features={PREMIUM_FEATURES}
            isPremium={isPremium}
          />
        </View>

        {/* Current Subscription Status */}
        {isPremium && subscription && (
          <View style={styles.subscriptionStatus}>
            <Text style={styles.statusTitle}>Your Subscription</Text>
            <View style={styles.statusCard}>
              <View style={styles.statusHeader}>
                <Crown size={24} color="#FFD700" />
                <View style={styles.statusInfo}>
                  <Text style={styles.statusPlan}>
                    {availablePlans.find(p => p.id === subscription.planId)?.name || 'Premium'} Plan
                  </Text>
                  <Text style={styles.statusPrice}>
                    ${subscription.price.toFixed(2)}/{subscription.billingCycle.replace('_', ' ')}
                  </Text>
                </View>
              </View>
              <Text style={styles.statusExpiry}>
                {subscription.autoRenew
                  ? `Renews on ${new Date(subscription.endDate).toLocaleDateString()}`
                  : `Expires on ${new Date(subscription.endDate).toLocaleDateString()}`
                }
              </Text>
            </View>
          </View>
        )}

        {/* Subscription Plans */}
        {!isPremium && (
          <View style={styles.plansSection}>
            <Text style={styles.sectionTitle}>Choose Your Plan</Text>
            <View style={styles.plansContainer}>
              {availablePlans.map((plan, index) => (
                <AnimatedSubscriptionCard
                  key={plan.id}
                  plan={plan}
                  isSelected={selectedPlan === plan.id}
                  onSelect={handlePlanSelect}
                  index={index}
                />
              ))}
            </View>
          </View>
        )}

        {/* Subscribe Button or Manage Subscription */}
        <View style={styles.subscribeSection}>
          {!isPremium ? (
            <TouchableOpacity
              style={[styles.subscribeButton, isProcessingPayment && styles.disabledButton]}
              onPress={handleSubscribe}
              disabled={isProcessingPayment}
            >
              <LinearGradient
                colors={['#8B5CF6', '#EC4899']}
                style={styles.subscribeGradient}
              >
                <Crown size={20} color="white" />
                <Text style={styles.subscribeButtonText}>
                  {isProcessingPayment
                    ? 'Processing...'
                    : `Start Premium - ${selectedPlanData?.price ? `$${selectedPlanData.price.toFixed(2)}` : '$19.99'}`
                  }
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          ) : (
            <View style={styles.premiumActiveSection}>
              <View style={styles.premiumActiveCard}>
                <Crown size={32} color="#FFD700" />
                <Text style={styles.premiumActiveTitle}>Premium Active</Text>
                <Text style={styles.premiumActiveSubtitle}>
                  Enjoy all premium features!
                </Text>
              </View>
            </View>
          )}

          <TouchableOpacity style={styles.restoreButton} onPress={handleRestorePurchases}>
            <Text style={styles.restoreButtonText}>Restore Purchases</Text>
          </TouchableOpacity>
        </View>

        {/* Terms */}
        <View style={styles.termsSection}>
          <Text style={styles.termsText}>
            Subscription automatically renews unless auto-renew is turned off at least 24 hours before the end of the current period.
          </Text>
          <View style={styles.termsLinks}>
            <TouchableOpacity>
              <Text style={styles.termsLink}>Terms of Service</Text>
            </TouchableOpacity>
            <Text style={styles.termsSeparator}>•</Text>
            <TouchableOpacity>
              <Text style={styles.termsLink}>Privacy Policy</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Payment Processing Modal */}
      <PaymentProcessingModal
        visible={showPaymentModal}
        plan={selectedPlanData}
        isProcessing={isProcessingPayment}
        isSuccess={paymentSuccess}
        isError={paymentError}
        errorMessage={error}
        onClose={closePaymentModal}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: '#8B5CF6',
  },
  backButton: {
    padding: theme.spacing.sm,
  },
  headerTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold,
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  heroSection: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xxl,
    paddingHorizontal: theme.spacing.lg,
    position: 'relative',
  },
  crownContainer: {
    marginBottom: theme.spacing.lg,
    position: 'relative',
  },
  sparklesContainer: {
    position: 'absolute',
    top: -10,
    left: -10,
    right: -10,
    bottom: -10,
  },
  sparkle1: {
    position: 'absolute',
    top: 0,
    right: 0,
  },
  sparkle2: {
    position: 'absolute',
    bottom: 0,
    left: 0,
  },
  sparkle3: {
    position: 'absolute',
    top: '50%',
    right: -5,
  },
  heroTitle: {
    fontSize: theme.fontSize.xxxl,
    fontWeight: theme.fontWeight.bold,
    color: 'white',
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  heroSubtitle: {
    fontSize: theme.fontSize.base,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.md,
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.full,
    borderWidth: 1,
    borderColor: '#FFD700',
    marginTop: theme.spacing.md,
  },
  premiumBadgeText: {
    color: '#FFD700',
    fontSize: theme.fontSize.sm,
    fontWeight: theme.fontWeight.bold,
  },
  featuresSection: {
    backgroundColor: 'white',
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  subscriptionStatus: {
    backgroundColor: 'white',
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  statusTitle: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  statusCard: {
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    borderWidth: 1,
    borderColor: '#FFD700',
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  statusInfo: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  statusPlan: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.text,
  },
  statusPrice: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray600,
  },
  statusExpiry: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray600,
    textAlign: 'center',
  },
  plansSection: {
    backgroundColor: theme.colors.gray50,
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  plansContainer: {
    gap: theme.spacing.md,
  },
  subscribeSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.xl,
    backgroundColor: 'white',
  },
  subscribeButton: {
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
    marginBottom: theme.spacing.lg,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  disabledButton: {
    opacity: 0.7,
  },
  subscribeGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.lg,
    gap: theme.spacing.sm,
  },
  subscribeButtonText: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.bold,
    color: 'white',
  },
  premiumActiveSection: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  premiumActiveCard: {
    alignItems: 'center',
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    borderWidth: 2,
    borderColor: '#FFD700',
  },
  premiumActiveTitle: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: '#FFD700',
    marginTop: theme.spacing.sm,
  },
  premiumActiveSubtitle: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray[600],
    marginTop: theme.spacing.xs,
  },
  restoreButton: {
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
  },
  restoreButtonText: {
    fontSize: theme.fontSize.base,
    color: theme.colors.primary,
  },
  termsSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.lg,
    backgroundColor: theme.colors.gray[50],
  },
  termsText: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.gray[600],
    textAlign: 'center',
    lineHeight: 18,
    marginBottom: theme.spacing.md,
  },
  termsLinks: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  termsLink: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.primary,
  },
  termsSeparator: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.gray[400],
  },
});

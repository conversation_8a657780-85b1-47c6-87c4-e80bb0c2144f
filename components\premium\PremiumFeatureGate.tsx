import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Crown, Lock, Sparkles, ArrowRight } from 'lucide-react-native';
import { usePremiumStore } from '@/stores/premiumStore';
import { PremiumFeatureType, PREMIUM_FEATURES } from '@/types/premium';
import { theme } from '@/constants/theme';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

const { width } = Dimensions.get('window');

interface PremiumFeatureGateProps {
  feature: PremiumFeatureType;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradeModal?: boolean;
  onUpgradePress?: () => void;
}

export default function PremiumFeatureGate({
  feature,
  children,
  fallback,
  showUpgradeModal = true,
  onUpgradePress,
}: PremiumFeatureGateProps) {
  const router = useRouter();
  const { hasFeature, checkFeatureAccess } = usePremiumStore();
  const [showModal, setShowModal] = React.useState(false);

  const featureAccess = checkFeatureAccess(feature);
  const featureInfo = PREMIUM_FEATURES.find(f => f.id === feature);

  const handleFeatureRequest = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    if (showUpgradeModal) {
      setShowModal(true);
    } else if (onUpgradePress) {
      onUpgradePress();
    } else {
      router.push('/premium');
    }
  };

  const handleUpgrade = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    }
    
    setShowModal(false);
    if (onUpgradePress) {
      onUpgradePress();
    } else {
      router.push('/premium');
    }
  };

  const closeModal = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setShowModal(false);
  };

  // If user has access to the feature, render children
  if (featureAccess.canAccess) {
    return <>{children}</>;
  }

  // If custom fallback is provided, use it
  if (fallback) {
    return <>{fallback}</>;
  }

  // Default fallback: show upgrade prompt
  return (
    <>
      <TouchableOpacity
        style={styles.upgradePrompt}
        onPress={handleFeatureRequest}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={['rgba(139, 92, 246, 0.1)', 'rgba(236, 72, 153, 0.1)']}
          style={styles.upgradeGradient}
        >
          <View style={styles.upgradeContent}>
            <View style={styles.upgradeIcon}>
              <Crown size={24} color="#FFD700" />
            </View>
            
            <View style={styles.upgradeText}>
              <Text style={styles.upgradeTitle}>
                {featureInfo?.name || 'Premium Feature'}
              </Text>
              <Text style={styles.upgradeDescription}>
                {featureAccess.reason || 'This feature requires a Premium subscription'}
              </Text>
            </View>
            
            <View style={styles.upgradeArrow}>
              <ArrowRight size={20} color={theme.colors.primary} />
            </View>
          </View>
        </LinearGradient>
      </TouchableOpacity>

      {/* Upgrade Modal */}
      <Modal
        visible={showModal}
        transparent
        animationType="fade"
        onRequestClose={closeModal}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalBackdrop} />
          
          <View style={styles.modal}>
            <LinearGradient
              colors={['#8B5CF6', '#EC4899']}
              style={styles.modalHeader}
            >
              <View style={styles.modalIconContainer}>
                <Crown size={32} color="#FFD700" />
                <View style={styles.sparklesContainer}>
                  <Sparkles size={16} color="#FFD700" style={styles.sparkle1} />
                  <Sparkles size={12} color="#FFD700" style={styles.sparkle2} />
                </View>
              </View>
              
              <Text style={styles.modalTitle}>Unlock Premium</Text>
              <Text style={styles.modalSubtitle}>
                Get access to {featureInfo?.name || 'this feature'} and more!
              </Text>
            </LinearGradient>
            
            <View style={styles.modalContent}>
              {featureInfo && (
                <View style={styles.featureHighlight}>
                  <View style={styles.featureIconContainer}>
                    <Lock size={20} color={theme.colors.primary} />
                  </View>
                  <View style={styles.featureDetails}>
                    <Text style={styles.featureName}>{featureInfo.name}</Text>
                    <Text style={styles.featureDesc}>{featureInfo.description}</Text>
                  </View>
                </View>
              )}
              
              {featureAccess.featureLimit && (
                <View style={styles.limitInfo}>
                  <Text style={styles.limitTitle}>Current Usage</Text>
                  <View style={styles.limitBar}>
                    <View 
                      style={[
                        styles.limitProgress,
                        {
                          width: `${(featureAccess.featureLimit.current / featureAccess.featureLimit.limit) * 100}%`
                        }
                      ]}
                    />
                  </View>
                  <Text style={styles.limitText}>
                    {featureAccess.featureLimit.current} / {featureAccess.featureLimit.limit} used
                  </Text>
                  {featureAccess.featureLimit.resetDate && (
                    <Text style={styles.resetText}>
                      Resets {featureAccess.featureLimit.resetDate.toLocaleDateString()}
                    </Text>
                  )}
                </View>
              )}
              
              <View style={styles.benefitsList}>
                <Text style={styles.benefitsTitle}>Premium Benefits:</Text>
                <View style={styles.benefit}>
                  <Crown size={16} color="#FFD700" />
                  <Text style={styles.benefitText}>Unlimited access to all features</Text>
                </View>
                <View style={styles.benefit}>
                  <Crown size={16} color="#FFD700" />
                  <Text style={styles.benefitText}>Priority customer support</Text>
                </View>
                <View style={styles.benefit}>
                  <Crown size={16} color="#FFD700" />
                  <Text style={styles.benefitText}>Exclusive premium content</Text>
                </View>
              </View>
            </View>
            
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.upgradeButton}
                onPress={handleUpgrade}
              >
                <LinearGradient
                  colors={['#8B5CF6', '#EC4899']}
                  style={styles.upgradeButtonGradient}
                >
                  <Crown size={20} color="white" />
                  <Text style={styles.upgradeButtonText}>Upgrade to Premium</Text>
                </LinearGradient>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={closeModal}
              >
                <Text style={styles.cancelButtonText}>Maybe Later</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  upgradePrompt: {
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    marginVertical: theme.spacing.sm,
  },
  upgradeGradient: {
    padding: theme.spacing.md,
  },
  upgradeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.md,
  },
  upgradeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  upgradeText: {
    flex: 1,
  },
  upgradeTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.text,
    marginBottom: 2,
  },
  upgradeDescription: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray600,
    lineHeight: 18,
  },
  upgradeArrow: {
    padding: theme.spacing.xs,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modal: {
    width: width * 0.9,
    maxWidth: 400,
    backgroundColor: 'white',
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 20,
  },
  modalHeader: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
    position: 'relative',
  },
  modalIconContainer: {
    marginBottom: theme.spacing.md,
    position: 'relative',
  },
  sparklesContainer: {
    position: 'absolute',
    top: -8,
    left: -8,
    right: -8,
    bottom: -8,
  },
  sparkle1: {
    position: 'absolute',
    top: 0,
    right: 0,
  },
  sparkle2: {
    position: 'absolute',
    bottom: 0,
    left: 0,
  },
  modalTitle: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: 'white',
    marginBottom: theme.spacing.xs,
  },
  modalSubtitle: {
    fontSize: theme.fontSize.base,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  modalContent: {
    padding: theme.spacing.lg,
  },
  featureHighlight: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  featureIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.md,
  },
  featureDetails: {
    flex: 1,
  },
  featureName: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.text,
    marginBottom: 2,
  },
  featureDesc: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray600,
  },
  limitInfo: {
    marginBottom: theme.spacing.lg,
  },
  limitTitle: {
    fontSize: theme.fontSize.sm,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  limitBar: {
    height: 8,
    backgroundColor: theme.colors.gray200,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: theme.spacing.xs,
  },
  limitProgress: {
    height: '100%',
    backgroundColor: theme.colors.primary,
  },
  limitText: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.gray600,
  },
  resetText: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.gray500,
    marginTop: 2,
  },
  benefitsList: {
    marginBottom: theme.spacing.lg,
  },
  benefitsTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  benefit: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  benefitText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray600,
  },
  modalActions: {
    padding: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  upgradeButton: {
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
  },
  upgradeButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  upgradeButtonText: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.bold,
    color: 'white',
  },
  cancelButton: {
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
  },
  cancelButtonText: {
    fontSize: theme.fontSize.base,
    color: theme.colors.gray600,
  },
});

import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { Crown, Check, Star } from 'lucide-react-native';
import { SubscriptionPlan } from '@/types/premium';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

interface AnimatedSubscriptionCardProps {
  plan: SubscriptionPlan;
  isSelected: boolean;
  onSelect: (planId: string) => void;
  index: number;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export default function AnimatedSubscriptionCard({
  plan,
  isSelected,
  onSelect,
  index,
}: AnimatedSubscriptionCardProps) {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const selectedScale = useSharedValue(isSelected ? 1.05 : 1);
  const borderWidth = useSharedValue(isSelected ? 2 : 1);
  const popularBadgeScale = useSharedValue(plan.popular ? 1 : 0);

  useEffect(() => {
    // Entrance animation with stagger
    const delay = index * 100;
    
    setTimeout(() => {
      scale.value = withSpring(1, {
        damping: 15,
        stiffness: 150,
      });
      opacity.value = withTiming(1, { duration: 300 });
    }, delay);

    // Popular badge animation
    if (plan.popular) {
      setTimeout(() => {
        popularBadgeScale.value = withSpring(1, {
          damping: 12,
          stiffness: 200,
        });
      }, delay + 200);
    }
  }, []);

  useEffect(() => {
    selectedScale.value = withSpring(isSelected ? 1.05 : 1, {
      damping: 15,
      stiffness: 200,
    });
    borderWidth.value = withTiming(isSelected ? 2 : 1, { duration: 200 });
  }, [isSelected]);

  const handlePress = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    // Animate press
    selectedScale.value = withSpring(0.95, { damping: 15, stiffness: 300 }, () => {
      selectedScale.value = withSpring(isSelected ? 1.05 : 1, {
        damping: 15,
        stiffness: 200,
      });
    });

    onSelect(plan.id);
  };

  const cardStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value * selectedScale.value },
      ],
      opacity: opacity.value,
      borderWidth: borderWidth.value,
    };
  });

  const popularBadgeStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: popularBadgeScale.value }],
      opacity: popularBadgeScale.value,
    };
  });

  const checkmarkStyle = useAnimatedStyle(() => {
    const checkScale = isSelected ? 1 : 0;
    return {
      transform: [{ scale: withSpring(checkScale, { damping: 15, stiffness: 300 }) }],
    };
  });

  const savingsStyle = useAnimatedStyle(() => {
    const savingsOpacity = plan.savings ? 1 : 0;
    return {
      opacity: withTiming(savingsOpacity, { duration: 300 }),
    };
  });

  return (
    <AnimatedTouchableOpacity
      style={[
        styles.card,
        cardStyle,
        {
          borderColor: isSelected ? theme.colors.primary : theme.colors.gray200,
          backgroundColor: isSelected ? theme.colors.primary + '10' : theme.colors.white,
        },
      ]}
      onPress={handlePress}
      activeOpacity={0.9}
    >
      {/* Popular Badge */}
      {plan.popular && (
        <Animated.View style={[styles.popularBadge, popularBadgeStyle]}>
          <LinearGradient
            colors={['#FFD700', '#FFA500']}
            style={styles.popularBadgeGradient}
          >
            <Star size={12} color="white" />
            <Text style={styles.popularBadgeText}>Most Popular</Text>
          </LinearGradient>
        </Animated.View>
      )}

      {/* Selection Indicator */}
      <Animated.View style={[styles.checkmark, checkmarkStyle]}>
        <View style={styles.checkmarkCircle}>
          <Check size={16} color="white" />
        </View>
      </Animated.View>

      {/* Plan Content */}
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[
            styles.duration,
            { color: isSelected ? theme.colors.primary : theme.colors.text }
          ]}>
            {plan.duration}
          </Text>
          
          {/* Savings Badge */}
          {plan.savings && (
            <Animated.View style={[styles.savingsBadge, savingsStyle]}>
              <Text style={styles.savingsText}>{plan.savings}</Text>
            </Animated.View>
          )}
        </View>

        <View style={styles.pricing}>
          {plan.originalPrice && (
            <Text style={styles.originalPrice}>
              ${plan.originalPrice.toFixed(2)}
            </Text>
          )}
          <Text style={[
            styles.price,
            { color: isSelected ? theme.colors.primary : theme.colors.text }
          ]}>
            ${plan.price.toFixed(2)}
          </Text>
        </View>

        <Text style={styles.pricePerMonth}>{plan.pricePerMonth}</Text>

        {/* Features Preview */}
        <View style={styles.features}>
          <Text style={styles.featuresTitle}>Includes:</Text>
          {plan.features.slice(0, 3).map((feature, idx) => (
            <View key={idx} style={styles.featureItem}>
              <Check size={12} color={theme.colors.success} />
              <Text style={styles.featureText}>
                {feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Text>
            </View>
          ))}
          {plan.features.length > 3 && (
            <Text style={styles.moreFeatures}>
              +{plan.features.length - 3} more features
            </Text>
          )}
        </View>

        {/* Trial Info */}
        {plan.trialDays && (
          <View style={styles.trialInfo}>
            <Crown size={14} color={theme.colors.primary} />
            <Text style={styles.trialText}>
              {plan.trialDays} days free trial
            </Text>
          </View>
        )}
      </View>
    </AnimatedTouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    left: theme.spacing.md,
    right: theme.spacing.md,
    zIndex: 1,
  },
  popularBadgeGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 12,
    gap: 4,
  },
  popularBadgeText: {
    color: 'white',
    fontSize: theme.fontSize.xs,
    fontWeight: theme.fontWeight.bold,
  },
  checkmark: {
    position: 'absolute',
    top: theme.spacing.md,
    right: theme.spacing.md,
    zIndex: 1,
  },
  checkmarkCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    marginTop: 8, // Remove dynamic reference to plan.popular
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.sm,
  },
  duration: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.bold,
  },
  savingsBadge: {
    backgroundColor: theme.colors.success,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  savingsText: {
    color: 'white',
    fontSize: theme.fontSize.xs,
    fontWeight: theme.fontWeight.medium,
  },
  pricing: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
    marginBottom: 4,
  },
  originalPrice: {
    fontSize: theme.fontSize.base,
    color: theme.colors.gray500,
    textDecorationLine: 'line-through',
  },
  price: {
    fontSize: theme.fontSize.xxl,
    fontWeight: theme.fontWeight.bold,
  },
  pricePerMonth: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray600,
    marginBottom: theme.spacing.md,
  },
  features: {
    marginBottom: theme.spacing.sm,
  },
  featuresTitle: {
    fontSize: theme.fontSize.sm,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.gray700,
    marginBottom: theme.spacing.xs,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
    marginBottom: 2,
  },
  featureText: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.gray600,
  },
  moreFeatures: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.primary,
    fontWeight: theme.fontWeight.medium,
    marginTop: 2,
  },
  trialInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
    marginTop: theme.spacing.xs,
  },
  trialText: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.primary,
    fontWeight: theme.fontWeight.medium,
  },
});
